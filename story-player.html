<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Story Player - StoryChoice</title>
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#1a1a2e">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --secondary-color: #ec4899;
            --accent-color: #f59e0b;
            --bg-dark: #0f0f23;
            --bg-card: #1a1a2e;
            --bg-gradient: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            --text-primary: #ffffff;
            --text-secondary: #a1a1aa;
            --text-muted: #71717a;
            --border-color: #27272a;
            --success-color: #10b981;
            --error-color: #ef4444;
            --warning-color: #f59e0b;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-gradient);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 1rem;
        }

        .story-header {
            text-align: center;
            padding: 2rem 0;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 2rem;
        }

        .story-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .story-meta {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
            color: var(--text-secondary);
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .story-player {
            background: var(--bg-card);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid var(--border-color);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
            margin-bottom: 2rem;
            min-height: 400px;
        }

        .story-content {
            margin-bottom: 2rem;
        }

        .story-text {
            font-size: 1.2rem;
            line-height: 1.8;
            margin-bottom: 2rem;
            text-align: justify;
        }

        .choices-container {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .choice-button {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            border: none;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
            position: relative;
            overflow: hidden;
        }

        .choice-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        .choice-button:active {
            transform: translateY(0);
        }

        .choice-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .choice-button:hover::before {
            left: 100%;
        }

        .ending-container {
            text-align: center;
            padding: 2rem;
            background: rgba(99, 102, 241, 0.1);
            border-radius: 15px;
            border: 1px solid rgba(99, 102, 241, 0.3);
        }

        .ending-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--accent-color);
        }

        .ending-text {
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        .story-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
            border-top: 1px solid var(--border-color);
        }

        .control-button {
            background: var(--bg-dark);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .control-button:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .progress-bar {
            background: var(--bg-dark);
            height: 4px;
            border-radius: 2px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 2px;
        }

        .story-selector {
            background: var(--bg-card);
            border-radius: 15px;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
        }

        .story-list {
            display: grid;
            gap: 1rem;
        }

        .story-item {
            background: var(--bg-dark);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .story-item:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .story-item-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .story-item-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: var(--text-secondary);
        }

        .error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: var(--error-color);
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }

        @media (max-width: 768px) {
            .container {
                padding: 0.5rem;
            }
            
            .story-title {
                font-size: 2rem;
            }
            
            .story-meta {
                flex-direction: column;
                gap: 1rem;
            }
            
            .story-player {
                padding: 1.5rem;
            }
            
            .choice-button {
                padding: 1rem;
                font-size: 0.95rem;
            }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .choice-selected {
            background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
            transform: scale(0.98);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Story Selection -->
        <div id="storySelector" class="story-selector">
            <h2>📚 Wähle eine Geschichte</h2>
            <div id="storyList" class="story-list">
                <div class="loading">Lade Geschichten...</div>
            </div>
        </div>

        <!-- Story Player -->
        <div id="storyPlayer" class="story-player" style="display: none;">
            <div class="story-header">
                <h1 id="storyTitle" class="story-title"></h1>
                <div class="story-meta">
                    <div class="meta-item">
                        <span>📖</span>
                        <span id="storyGenre"></span>
                    </div>
                    <div class="meta-item">
                        <span>⏱️</span>
                        <span id="storyTime"></span>
                    </div>
                    <div class="meta-item">
                        <span>✍️</span>
                        <span id="storyAuthor"></span>
                    </div>
                </div>
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill" style="width: 0%"></div>
                </div>
            </div>

            <div id="storyContent" class="story-content">
                <!-- Story content will be dynamically inserted here -->
            </div>

            <div class="story-controls">
                <button class="control-button" onclick="restartStory()">🔄 Neustart</button>
                <button class="control-button" onclick="saveProgress()">💾 Speichern</button>
                <button class="control-button" onclick="goBack()">← Zurück</button>
                <button class="control-button" onclick="showStorySelector()">📚 Andere Geschichte</button>
            </div>
        </div>
    </div>

    <script>
        class StoryPlayer {
            constructor() {
                this.currentStory = null;
                this.currentPageId = null;
                this.visitedPages = [];
                this.choices = [];
                this.startTime = null;
                this.init();
            }

            async init() {
                await this.loadAvailableStories();
                this.loadSavedProgress();
            }

            async loadAvailableStories() {
                try {
                    // Try to load from API first
                    const response = await fetch('/api/stories');
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success && data.stories.length > 0) {
                            this.displayStoryList(data.stories);
                            return;
                        }
                    }
                } catch (error) {
                    console.log('API not available, loading sample story');
                }

                // Fallback to sample story
                this.loadSampleStory();
            }

            loadSampleStory() {
                // Sample story for demonstration
                const sampleStory = {
                    id: "sample_story",
                    title: "Das Geheimnis der Zeitreise",
                    description: "Eine mysteriöse Entdeckung führt dich in ein Abenteuer durch die Zeit.",
                    genre: "Mystery",
                    reading_time: 15,
                    author: "StoryChoice Demo",
                    pages: {
                        "1": {
                            id: "1",
                            text: "Du stehst vor einer alten Holztür in einem verlassenen Herrenhaus. Ein seltsames, pulsierendes Leuchten dringt durch die Ritzen. Ein kalter Windzug streicht über dein Gesicht, während du überlegst, was sich dahinter verbergen mag.",
                            choices: [
                                { text: "Die Tür sofort öffnen", next_page_id: "2A" },
                                { text: "Erst die Symbole an der Tür untersuchen", next_page_id: "2B" }
                            ]
                        },
                        "2A": {
                            id: "2A",
                            text: "Mit einem tiefen Atemzug drückst du die Klinke. Die Tür schwingt knarrend auf und gibt den Blick frei auf einen spiralförmigen Korridor, dessen Wände mit leuchtenden Symbolen bedeckt sind. Ein starker Sog zieht dich unwiderstehlich hinein.",
                            choices: [
                                { text: "Dem Sog folgen", next_page_id: "3A" },
                                { text: "Versuchen zu widerstehen", next_page_id: "3B" }
                            ]
                        },
                        "2B": {
                            id: "2B",
                            text: "Du untersuchst die Symbole genauer. Sie scheinen eine Art Warnung zu sein - alte Runen, die vor Gefahren warnen. Plötzlich verstehst du ihre Bedeutung: 'Nur die Mutigen sollen eintreten, denn die Zeit selbst liegt in euren Händen.'",
                            choices: [
                                { text: "Trotz der Warnung eintreten", next_page_id: "2A" },
                                { text: "Umkehren und gehen", next_page_id: "ending_safe" }
                            ]
                        },
                        "3A": {
                            id: "3A",
                            text: "Du lässt dich von dem Sog erfassen und wirst durch den Korridor geschleudert. Als du wieder zu dir kommst, findest du dich in einem prächtigen Ballsaal wieder, der von Kerzen erleuchtet wird. Menschen in historischen Kostümen tanzen, als wäre die Zeit stehengeblieben.",
                            choices: [
                                { text: "Zu den Tanzenden gehen", next_page_id: "ending_dance" },
                                { text: "Nach einem Ausgang suchen", next_page_id: "ending_escape" }
                            ]
                        },
                        "3B": {
                            id: "3B",
                            text: "Du stemmst dich gegen den Sog und schaffst es, dich am Türrahmen festzuhalten. Dabei entdeckst du einen versteckten Mechanismus. Als du ihn betätigst, öffnet sich eine geheime Passage neben der Tür.",
                            choices: [
                                { text: "Die geheime Passage erkunden", next_page_id: "ending_secret" },
                                { text: "Doch durch die Haupttür gehen", next_page_id: "3A" }
                            ]
                        },
                        "ending_safe": {
                            id: "ending_safe",
                            text: "Du entscheidest dich für die Vorsicht und verlässt das Herrenhaus. Manchmal ist es klüger, Geheimnisse ruhen zu lassen. Du gehst nach Hause, aber die Erinnerung an das mysteriöse Leuchten wird dich für immer begleiten.",
                            is_ending: true,
                            ending_type: "happy_compromise"
                        },
                        "ending_dance": {
                            id: "ending_dance",
                            text: "Du trittst in den Ballsaal und wirst sofort in den Tanz hineingezogen. Die Zeit verschwimmt, und du tanzt durch die Jahrhunderte. Als der Tanz endet, findest du dich wieder vor dem Herrenhaus - aber du bist um eine magische Erfahrung reicher.",
                            is_ending: true,
                            ending_type: "happy_twist"
                        },
                        "ending_escape": {
                            id: "ending_escape",
                            text: "Du findest einen Ausgang und entkommst dem zeitlosen Ballsaal. Draußen stellst du fest, dass nur wenige Minuten vergangen sind, obwohl es sich wie Stunden anfühlte. Du hast das Geheimnis der Zeitreise entdeckt und kannst nun zwischen den Zeiten wandeln.",
                            is_ending: true,
                            ending_type: "happy_triumph"
                        },
                        "ending_secret": {
                            id: "ending_secret",
                            text: "Die geheime Passage führt dich zu einer Bibliothek voller alter Bücher über Zeitreisen. Du verbringst Stunden damit, die Geheimnisse zu studieren, und wirst schließlich zum Hüter dieses Wissens. Ein bittersweet Ende - du hast große Macht erlangt, aber bist nun für immer an diesen Ort gebunden.",
                            is_ending: true,
                            ending_type: "bittersweet"
                        }
                    }
                };

                this.displayStoryList([sampleStory]);
            }

            displayStoryList(stories) {
                const storyList = document.getElementById('storyList');
                storyList.innerHTML = '';

                stories.forEach(story => {
                    const storyItem = document.createElement('div');
                    storyItem.className = 'story-item';
                    storyItem.onclick = () => this.loadStory(story);

                    storyItem.innerHTML = `
                        <div class="story-item-title">${this.escapeHtml(story.title)}</div>
                        <div class="story-item-description">${this.escapeHtml(story.description)}</div>
                        <div style="margin-top: 0.5rem; color: var(--text-muted); font-size: 0.8rem;">
                            ${story.genre} • ${story.reading_time} Min • ${story.author || 'Unbekannt'}
                        </div>
                    `;

                    storyList.appendChild(storyItem);
                });
            }

            async loadStory(storyData) {
                try {
                    // If we only have metadata, fetch full story
                    if (!storyData.pages) {
                        const response = await fetch(`/api/stories/${storyData.id}`);
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                storyData = data.story;
                            }
                        }
                    }

                    this.currentStory = storyData;
                    this.currentPageId = storyData.start_page_id || "1";
                    this.visitedPages = [];
                    this.choices = [];
                    this.startTime = new Date();

                    this.displayStoryHeader();
                    this.displayCurrentPage();
                    this.showStoryPlayer();
                    this.saveProgress();
                } catch (error) {
                    this.showError('Fehler beim Laden der Geschichte: ' + error.message);
                }
            }

            displayStoryHeader() {
                document.getElementById('storyTitle').textContent = this.currentStory.title;
                document.getElementById('storyGenre').textContent = this.currentStory.genre;
                document.getElementById('storyTime').textContent = `${this.currentStory.reading_time} Min`;
                document.getElementById('storyAuthor').textContent = this.currentStory.author || 'Unbekannt';
            }

            displayCurrentPage() {
                const page = this.currentStory.pages[this.currentPageId];
                if (!page) {
                    this.showError('Seite nicht gefunden: ' + this.currentPageId);
                    return;
                }

                // Add to visited pages if not already there
                if (!this.visitedPages.includes(this.currentPageId)) {
                    this.visitedPages.push(this.currentPageId);
                }

                const content = document.getElementById('storyContent');
                content.innerHTML = '';
                content.className = 'story-content fade-in';

                // Display story text
                const textElement = document.createElement('div');
                textElement.className = 'story-text';
                textElement.textContent = page.text;
                content.appendChild(textElement);

                // Display choices or ending
                if (page.is_ending) {
                    this.displayEnding(page);
                } else if (page.choices && page.choices.length > 0) {
                    this.displayChoices(page.choices);
                } else {
                    this.showError('Diese Seite hat keine Entscheidungen oder Endmarkierung.');
                }

                this.updateProgress();
            }

            displayChoices(choices) {
                const choicesContainer = document.createElement('div');
                choicesContainer.className = 'choices-container';

                choices.forEach((choice, index) => {
                    const button = document.createElement('button');
                    button.className = 'choice-button';
                    button.textContent = choice.text;
                    button.onclick = () => this.makeChoice(choice, button);
                    choicesContainer.appendChild(button);
                });

                document.getElementById('storyContent').appendChild(choicesContainer);
            }

            displayEnding(page) {
                const endingContainer = document.createElement('div');
                endingContainer.className = 'ending-container';

                const endingTitle = this.getEndingTitle(page.ending_type);

                endingContainer.innerHTML = `
                    <div class="ending-title">${endingTitle}</div>
                    <div class="ending-text">
                        Du hast eine von vielen möglichen Geschichten erlebt.
                        Jede Entscheidung führt zu einem anderen Abenteuer.
                    </div>
                    <button class="choice-button" onclick="storyPlayer.restartStory()">
                        🔄 Geschichte neu erleben
                    </button>
                    <button class="choice-button" onclick="storyPlayer.showStorySelector()"
                            style="background: linear-gradient(135deg, var(--secondary-color), var(--accent-color)); margin-top: 1rem;">
                        📚 Andere Geschichte wählen
                    </button>
                `;

                document.getElementById('storyContent').appendChild(endingContainer);
                this.updateProgress(100);
            }

            makeChoice(choice, buttonElement) {
                // Visual feedback
                buttonElement.classList.add('choice-selected');

                // Disable all choice buttons
                const allButtons = document.querySelectorAll('.choice-button');
                allButtons.forEach(btn => {
                    btn.disabled = true;
                    if (btn !== buttonElement) {
                        btn.style.opacity = '0.5';
                    }
                });

                // Record choice
                this.choices.push({
                    pageId: this.currentPageId,
                    choiceText: choice.text,
                    nextPageId: choice.next_page_id,
                    timestamp: new Date()
                });

                // Navigate to next page after a short delay
                setTimeout(() => {
                    this.currentPageId = choice.next_page_id;
                    this.displayCurrentPage();
                }, 800);
            }

            getEndingTitle(endingType) {
                const endingTitles = {
                    'happy_triumph': '🎉 Triumphales Ende!',
                    'happy_compromise': '😊 Ein guter Kompromiss',
                    'happy_twist': '🎭 Überraschende Wendung!',
                    'tragic': '😢 Tragisches Ende',
                    'open': '🤔 Ein offenes Ende',
                    'bittersweet': '🍯 Bittersüßes Ende'
                };
                return endingTitles[endingType] || '📖 Geschichte beendet';
            }

            updateProgress(forcePercent = null) {
                let progressPercent;

                if (forcePercent !== null) {
                    progressPercent = forcePercent;
                } else {
                    const totalPages = Object.keys(this.currentStory.pages).length;
                    progressPercent = Math.round((this.visitedPages.length / totalPages) * 100);
                }

                document.getElementById('progressFill').style.width = `${progressPercent}%`;
            }

            restartStory() {
                if (this.currentStory) {
                    this.currentPageId = this.currentStory.start_page_id || "1";
                    this.visitedPages = [];
                    this.choices = [];
                    this.startTime = new Date();
                    this.displayCurrentPage();
                    this.saveProgress();
                }
            }

            goBack() {
                if (this.visitedPages.length > 1) {
                    // Remove current page from visited
                    this.visitedPages.pop();
                    // Go to previous page
                    this.currentPageId = this.visitedPages[this.visitedPages.length - 1];
                    // Remove the last choice
                    this.choices.pop();
                    this.displayCurrentPage();
                    this.saveProgress();
                }
            }

            showStoryPlayer() {
                document.getElementById('storySelector').style.display = 'none';
                document.getElementById('storyPlayer').style.display = 'block';
            }

            showStorySelector() {
                document.getElementById('storyPlayer').style.display = 'none';
                document.getElementById('storySelector').style.display = 'block';
            }

            saveProgress() {
                if (!this.currentStory) return;

                const progress = {
                    storyId: this.currentStory.id,
                    currentPageId: this.currentPageId,
                    visitedPages: this.visitedPages,
                    choices: this.choices,
                    startTime: this.startTime,
                    lastSaved: new Date()
                };

                localStorage.setItem('storyProgress_' + this.currentStory.id, JSON.stringify(progress));
            }

            loadSavedProgress() {
                // This could be expanded to show a list of saved games
                // For now, it's just a placeholder for the functionality
            }

            showError(message) {
                const content = document.getElementById('storyContent');
                content.innerHTML = `<div class="error">❌ ${this.escapeHtml(message)}</div>`;
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }
        }

        // Global functions for button onclick handlers
        function restartStory() {
            storyPlayer.restartStory();
        }

        function saveProgress() {
            storyPlayer.saveProgress();
            alert('Fortschritt gespeichert!');
        }

        function goBack() {
            storyPlayer.goBack();
        }

        function showStorySelector() {
            storyPlayer.showStorySelector();
        }

        // Initialize the story player
        let storyPlayer;
        document.addEventListener('DOMContentLoaded', () => {
            storyPlayer = new StoryPlayer();
        });
    </script>
</body>
</html>
