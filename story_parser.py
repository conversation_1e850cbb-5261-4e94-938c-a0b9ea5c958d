"""
Story Parser für automatische Strukturierung von Rohtext-Geschichten
"""

import re
import uuid
from typing import Dict, List, Tuple, Optional
from datetime import datetime


class StoryParser:
    """
    Parser für die Umwandlung von strukturiertem Rohtext in das JSON-Schema
    für interaktive Geschichten.
    """
    
    def __init__(self, raw_text: str):
        self.raw_text = raw_text.strip()
        self.parsed_story = {
            "id": str(uuid.uuid4()),
            "title": "",
            "description": "",
            "genre": "",
            "reading_time": 30,  # Standard: 30 Minuten
            "theme": "",
            "style": "",
            "start_page_id": "1",
            "author": "",
            "pages": {},
            "created_at": datetime.utcnow().isoformat(),
            "is_published": False
        }
        self.sections = []
        self.errors = []
        self.warnings = []
        
    def parse(self) -> Dict:
        """
        Hauptmethode zum Parsen des Rohtextes.
        
        Returns:
            Dict: Geparste Story im JSON-Format
        """
        try:
            self._split_sections()
            self._parse_metadata()
            self._parse_pages()
            self._parse_decisions_and_link_paths()
            self._validate_story()
            
            if self.errors:
                raise ValueError(f"Parsing-Fehler: {'; '.join(self.errors)}")
                
            return self.parsed_story
            
        except Exception as e:
            self.errors.append(str(e))
            raise
    
    def _split_sections(self):
        """
        Teilt den Rohtext in logische Abschnitte auf.
        """
        # Teile den Text an Trennlinien und Hauptüberschriften
        sections = re.split(r'\n\s*-----+\s*\n|\n(?=# [^#])', self.raw_text)
        
        self.sections = []
        for section in sections:
            section = section.strip()
            if section:
                self.sections.append(section)
    
    def _parse_metadata(self):
        """
        Extrahiert Titel und Beschreibung aus dem ersten Abschnitt.
        """
        if not self.sections:
            self.errors.append("Keine Abschnitte gefunden")
            return
            
        first_section = self.sections[0]
        lines = first_section.split('\n')
        
        # Titel extrahieren (erste Zeile mit #)
        title_match = re.search(r'^#\s*(.+)', first_section, re.MULTILINE)
        if title_match:
            self.parsed_story["title"] = title_match.group(1).strip()
        else:
            self.errors.append("Kein Titel gefunden")
        
        # Beschreibung extrahieren (Text in Kursiv)
        desc_match = re.search(r'\*(.+?)\*', first_section)
        if desc_match:
            self.parsed_story["description"] = desc_match.group(1).strip()
        else:
            # Fallback: Erste Textzeile nach dem Titel
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#') and not line.startswith('*'):
                    self.parsed_story["description"] = line
                    break
    
    def _parse_pages(self):
        """
        Extrahiert alle Seiten aus den Abschnitten.
        """
        for section in self.sections:
            # Suche nach Seiten-Überschriften
            page_matches = re.finditer(r'^##\s*Seite\s+([^:]+):\s*(.+)', section, re.MULTILINE)
            
            for match in page_matches:
                page_id = match.group(1).strip()
                page_title = match.group(2).strip()
                
                # Extrahiere den Text der Seite
                start_pos = match.end()
                
                # Finde das Ende der Seite (nächste Überschrift oder Ende des Abschnitts)
                next_header = re.search(r'\n##', section[start_pos:])
                if next_header:
                    end_pos = start_pos + next_header.start()
                    page_text = section[start_pos:end_pos].strip()
                else:
                    page_text = section[start_pos:].strip()
                
                # Bereinige den Text
                page_text = self._clean_page_text(page_text)
                
                # Bestimme, ob es eine End-Seite ist
                is_ending = self._is_ending_page(section, page_id)
                ending_type = self._get_ending_type(section, page_id) if is_ending else None
                
                self.parsed_story["pages"][page_id] = {
                    "id": page_id,
                    "text": page_text,
                    "choices": [],
                    "is_ending": is_ending
                }
                
                if ending_type:
                    self.parsed_story["pages"][page_id]["ending_type"] = ending_type
            
            # Suche nach Varianten-Überschriften (für Enden)
            variant_matches = re.finditer(r'^##\s*Variante\s+([^:]+):\s*(.+)', section, re.MULTILINE)
            
            for match in variant_matches:
                variant_id = match.group(1).strip()
                variant_title = match.group(2).strip()
                
                # Extrahiere den Text der Variante
                start_pos = match.end()
                next_header = re.search(r'\n##', section[start_pos:])
                if next_header:
                    end_pos = start_pos + next_header.start()
                    variant_text = section[start_pos:end_pos].strip()
                else:
                    variant_text = section[start_pos:].strip()
                
                variant_text = self._clean_page_text(variant_text)
                ending_type = self._map_variant_to_ending_type(variant_id)
                
                # Verwende variant_id als page_id für Enden
                page_id = f"ending_{variant_id.lower()}"
                
                self.parsed_story["pages"][page_id] = {
                    "id": page_id,
                    "text": variant_text,
                    "choices": [],
                    "is_ending": True,
                    "ending_type": ending_type
                }
    
    def _clean_page_text(self, text: str) -> str:
        """
        Bereinigt den Seitentext von Markdown-Formatierung und überflüssigen Zeichen.
        """
        # Entferne Markdown-Formatierung
        text = re.sub(r'\*\*(.+?)\*\*', r'\1', text)  # Fett
        text = re.sub(r'\*(.+?)\*', r'\1', text)      # Kursiv
        
        # Entferne Trennlinien
        text = re.sub(r'^-+$', '', text, flags=re.MULTILINE)
        
        # Entferne leere Zeilen am Anfang und Ende
        text = text.strip()
        
        # Normalisiere Leerzeichen
        text = re.sub(r'\n\s*\n', '\n\n', text)
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        return text
    
    def _is_ending_page(self, section: str, page_id: str) -> bool:
        """
        Bestimmt, ob eine Seite eine End-Seite ist.
        """
        # Prüfe, ob die Seite in einem ENDE-Abschnitt steht
        return '# ENDE' in section or 'Variante' in page_id
    
    def _get_ending_type(self, section: str, page_id: str) -> Optional[str]:
        """
        Bestimmt den Typ des Endes basierend auf dem Abschnitt.
        """
        if '# ENDE X' in section:
            return 'happy_triumph'
        elif '# ENDE Y' in section:
            return 'tragic'
        elif '# ENDE Z' in section:
            return 'open'
        return None
    
    def _map_variant_to_ending_type(self, variant_id: str) -> str:
        """
        Mappt Varianten-IDs zu Ending-Typen.
        """
        variant_mapping = {
            'X1': 'happy_triumph',
            'X2': 'happy_compromise', 
            'X3': 'happy_twist',
            'Y1': 'tragic',
            'Y2': 'open',
            'Y3': 'bittersweet',
            'Z1': 'tragic',
            'Z2': 'open',
            'Z3': 'bittersweet'
        }
        
        return variant_mapping.get(variant_id, 'open')
    
    def _parse_decisions_and_link_paths(self):
        """
        Extrahiert Entscheidungen und verknüpft sie mit den entsprechenden Seiten.
        """
        for section in self.sections:
            # Suche nach Entscheidungs-Blöcken
            decision_matches = re.finditer(r'\*\*([^*]+ENTSCHEIDUNG[^*]*)\*\*', section, re.IGNORECASE)
            
            for decision_match in decision_matches:
                decision_title = decision_match.group(1).strip()
                
                # Finde die Optionen nach der Entscheidung
                start_pos = decision_match.end()
                options_text = section[start_pos:]
                
                # Extrahiere Optionen
                option_matches = re.finditer(r'\*\*Option\s+([^*]+):\*\*\s*(.+?)(?=\*\*Option|\n\n|$)', 
                                            options_text, re.DOTALL)
                
                options = []
                for option_match in option_matches:
                    option_id = option_match.group(1).strip()
                    option_text = option_match.group(2).strip()
                    
                    # Bestimme die next_page_id basierend auf der Option
                    next_page_id = self._determine_next_page_id(decision_title, option_id, section)
                    
                    if next_page_id:
                        options.append({
                            "text": option_text,
                            "next_page_id": next_page_id
                        })
                
                # Finde die Seite, zu der diese Entscheidung gehört
                source_page_id = self._find_source_page_for_decision(section, decision_match.start())
                
                if source_page_id and source_page_id in self.parsed_story["pages"]:
                    self.parsed_story["pages"][source_page_id]["choices"] = options
    
    def _determine_next_page_id(self, decision_title: str, option_id: str, section: str) -> Optional[str]:
        """
        Bestimmt die next_page_id für eine Option basierend auf dem Kontext.
        """
        # Mapping für verschiedene Entscheidungstypen
        if "ERSTE ENTSCHEIDUNG" in decision_title:
            if "A" in option_id:
                return self._find_first_page_in_path("PFAD A")
            elif "B" in option_id:
                return self._find_first_page_in_path("PFAD B")
        
        elif "ZWEITE ENTSCHEIDUNG" in decision_title:
            if "PFAD A" in decision_title:
                if "A1" in option_id:
                    return self._find_first_page_in_path("PFAD A1")
                elif "A2" in option_id:
                    return self._find_first_page_in_path("PFAD A2")
            elif "PFAD B" in decision_title:
                if "B1" in option_id:
                    return self._find_first_page_in_path("PFAD B1")
                elif "B2" in option_id:
                    return self._find_first_page_in_path("PFAD B2")
        
        elif "FINALE ENTSCHEIDUNG" in decision_title:
            if "X" in option_id:
                return self._find_first_ending("X")
            elif "Y" in option_id:
                return self._find_first_ending("Y")
            elif "Z" in option_id:
                return self._find_first_ending("Z")
        
        return None
    
    def _find_first_page_in_path(self, path_name: str) -> Optional[str]:
        """
        Findet die erste Seite in einem bestimmten Pfad.
        """
        for section in self.sections:
            if f"# {path_name}" in section:
                # Suche nach der ersten Seite in diesem Abschnitt
                page_match = re.search(r'##\s*Seite\s+([^:]+):', section)
                if page_match:
                    return page_match.group(1).strip()
        return None
    
    def _find_first_ending(self, ending_letter: str) -> Optional[str]:
        """
        Findet das erste Ende für einen bestimmten Buchstaben.
        """
        for section in self.sections:
            if f"# ENDE {ending_letter}" in section:
                # Suche nach der ersten Variante in diesem Abschnitt
                variant_match = re.search(r'##\s*Variante\s+([^:]+):', section)
                if variant_match:
                    variant_id = variant_match.group(1).strip()
                    return f"ending_{variant_id.lower()}"
        return None
    
    def _find_source_page_for_decision(self, section: str, decision_pos: int) -> Optional[str]:
        """
        Findet die Seite, die vor einer Entscheidung steht.
        """
        # Suche rückwärts nach der letzten Seiten-Überschrift
        text_before = section[:decision_pos]
        page_matches = list(re.finditer(r'##\s*Seite\s+([^:]+):', text_before))
        
        if page_matches:
            return page_matches[-1].group(1).strip()
        
        return None
    
    def _validate_story(self):
        """
        Validiert die geparste Story gegen das Schema.
        """
        # Prüfe Pflichtfelder
        if not self.parsed_story["title"]:
            self.errors.append("Titel ist erforderlich")
        
        if not self.parsed_story["pages"]:
            self.errors.append("Keine Seiten gefunden")
        
        # Prüfe start_page_id
        if self.parsed_story["start_page_id"] not in self.parsed_story["pages"]:
            self.errors.append(f"Start-Seite '{self.parsed_story['start_page_id']}' nicht gefunden")
        
        # Prüfe Referenzintegrität
        for page_id, page in self.parsed_story["pages"].items():
            for choice in page.get("choices", []):
                next_page_id = choice.get("next_page_id")
                if next_page_id and next_page_id not in self.parsed_story["pages"]:
                    self.errors.append(f"Seite '{page_id}' verweist auf nicht existierende Seite '{next_page_id}'")
        
        # Prüfe Textlänge (2-4 Sätze)
        for page_id, page in self.parsed_story["pages"].items():
            text = page.get("text", "")
            sentence_count = len(re.findall(r'[.!?]+', text))
            if sentence_count < 2 or sentence_count > 4:
                self.warnings.append(f"Seite '{page_id}' hat {sentence_count} Sätze (empfohlen: 2-4)")
    
    def get_validation_report(self) -> Dict:
        """
        Gibt einen Validierungsbericht zurück.
        """
        return {
            "is_valid": len(self.errors) == 0,
            "errors": self.errors,
            "warnings": self.warnings,
            "statistics": {
                "page_count": len(self.parsed_story["pages"]),
                "ending_count": len([p for p in self.parsed_story["pages"].values() if p.get("is_ending")]),
                "choice_count": sum(len(p.get("choices", [])) for p in self.parsed_story["pages"].values())
            }
        }


def parse_story_from_text(raw_text: str) -> Tuple[Dict, Dict]:
    """
    Convenience-Funktion zum Parsen einer Story aus Rohtext.
    
    Args:
        raw_text: Der Rohtext der Geschichte
        
    Returns:
        Tuple[Dict, Dict]: (geparste_story, validierungsbericht)
    """
    parser = StoryParser(raw_text)
    
    try:
        story = parser.parse()
        validation_report = parser.get_validation_report()
        return story, validation_report
    except Exception as e:
        validation_report = parser.get_validation_report()
        validation_report["parse_error"] = str(e)
        return None, validation_report

