#!/usr/bin/env python3
"""
Test runner for the story player functionality
"""

import subprocess
import sys
import time
import webbrowser
from pathlib import Path

def main():
    print("🚀 Starting Story Player Test...")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("main.py").exists():
        print("❌ Error: main.py not found. Please run this script from the project root directory.")
        return
    
    if not Path("story-player.html").exists():
        print("❌ Error: story-player.html not found. Please make sure the story player file exists.")
        return
    
    print("✅ Found required files")
    
    # Add sample stories to database
    print("\n📚 Adding sample stories to database...")
    try:
        result = subprocess.run([sys.executable, "test_story_api.py"], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("✅ Sample stories added successfully")
            print(result.stdout)
        else:
            print("⚠️  Warning: Could not add sample stories")
            print("Error:", result.stderr)
    except subprocess.TimeoutExpired:
        print("⚠️  Warning: Database setup timed out")
    except Exception as e:
        print(f"⚠️  Warning: Database setup failed: {e}")
    
    # Start Flask server
    print("\n🌐 Starting Flask server...")
    try:
        # Start server in background
        server_process = subprocess.Popen([sys.executable, "main.py"], 
                                        stdout=subprocess.PIPE, 
                                        stderr=subprocess.PIPE)
        
        # Wait a moment for server to start
        time.sleep(3)
        
        # Check if server is running
        if server_process.poll() is None:
            print("✅ Flask server started successfully on http://localhost:5001")
            
            # Open browser
            print("\n🌐 Opening story player in browser...")
            try:
                webbrowser.open("http://localhost:5001/story-player.html")
                print("✅ Browser opened")
            except Exception as e:
                print(f"⚠️  Could not open browser automatically: {e}")
                print("Please manually open: http://localhost:5001/story-player.html")
            
            print("\n" + "=" * 50)
            print("🎮 STORY PLAYER TEST READY!")
            print("=" * 50)
            print("📖 Main page: http://localhost:5001/")
            print("🎮 Story Player: http://localhost:5001/story-player.html")
            print("📝 Story Upload: http://localhost:5001/story-upload.html")
            print("🔧 API Stories: http://localhost:5001/api/stories")
            print("=" * 50)
            print("\n🧪 Test Instructions:")
            print("1. The story player should load automatically")
            print("2. You should see sample stories to choose from")
            print("3. Click on a story to start playing")
            print("4. Make choices and see the story progress")
            print("5. Test the back button and restart functionality")
            print("6. Check that progress is saved in localStorage")
            print("\n⚠️  Known limitations:")
            print("- No service worker (offline functionality)")
            print("- Basic error handling")
            print("- Simple progress tracking")
            print("\n🛑 Press Ctrl+C to stop the server")
            
            # Keep server running
            try:
                server_process.wait()
            except KeyboardInterrupt:
                print("\n\n🛑 Stopping server...")
                server_process.terminate()
                server_process.wait()
                print("✅ Server stopped")
                
        else:
            print("❌ Failed to start Flask server")
            stdout, stderr = server_process.communicate()
            print("STDOUT:", stdout.decode())
            print("STDERR:", stderr.decode())
            
    except Exception as e:
        print(f"❌ Error starting server: {e}")

if __name__ == "__main__":
    main()
