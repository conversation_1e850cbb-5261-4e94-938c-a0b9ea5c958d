<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Story Upload - StoryChoice</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a3a 100%);
            color: #e2e8f0;
            min-height: 100vh;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #6366f1, #ec4899);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            color: #94a3b8;
        }

        .upload-section {
            background: rgba(30, 41, 59, 0.5);
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(100, 116, 139, 0.2);
        }

        .section-title {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: #f1f5f9;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 24px;
            background: linear-gradient(135deg, #6366f1, #ec4899);
            border-radius: 2px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #f1f5f9;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(100, 116, 139, 0.3);
            border-radius: 8px;
            color: #e2e8f0;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .form-group textarea {
            min-height: 400px;
            resize: vertical;
            font-family: 'Courier New', monospace;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
        }

        .btn-secondary {
            background: rgba(100, 116, 139, 0.2);
            color: #e2e8f0;
            border: 1px solid rgba(100, 116, 139, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(100, 116, 139, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
        }

        .btn-group {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .preview-section {
            background: rgba(30, 41, 59, 0.5);
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(100, 116, 139, 0.2);
            display: none;
        }

        .preview-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: rgba(15, 23, 42, 0.8);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid rgba(100, 116, 139, 0.2);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #6366f1;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #94a3b8;
            font-size: 0.9rem;
        }

        .validation-report {
            background: rgba(15, 23, 42, 0.8);
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
        }

        .validation-success {
            border-left: 4px solid #10b981;
        }

        .validation-warning {
            border-left: 4px solid #f59e0b;
        }

        .validation-error {
            border-left: 4px solid #ef4444;
        }

        .validation-list {
            list-style: none;
            margin-top: 10px;
        }

        .validation-list li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }

        .validation-list li::before {
            content: '•';
            position: absolute;
            left: 0;
            color: #6366f1;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 3px solid rgba(99, 102, 241, 0.3);
            border-radius: 50%;
            border-top: 3px solid #6366f1;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .help-section {
            background: rgba(30, 41, 59, 0.5);
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(100, 116, 139, 0.2);
        }

        .help-toggle {
            cursor: pointer;
            user-select: none;
        }

        .help-content {
            display: none;
            margin-top: 20px;
        }

        .help-content.active {
            display: block;
        }

        .format-example {
            background: rgba(15, 23, 42, 0.8);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            border-left: 4px solid #6366f1;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid;
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            border-color: #10b981;
            color: #10b981;
        }

        .alert-error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
            color: #ef4444;
        }

        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            border-color: #f59e0b;
            color: #f59e0b;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Story Upload</h1>
            <p>Lade deine interaktive Geschichte hoch und lass sie automatisch strukturieren</p>
        </div>

        <!-- Hilfe-Sektion -->
        <div class="help-section">
            <div class="section-title help-toggle" onclick="toggleHelp()">
                📖 Formatierungshilfe
            </div>
            <div class="help-content" id="helpContent">
                <p>Deine Geschichte sollte folgendem Format folgen:</p>
                
                <h4>1. Titel und Beschreibung:</h4>
                <div class="format-example"># Titel der Geschichte
*Kurze Beschreibung in Kursiv*
-----</div>

                <h4>2. Seiten:</h4>
                <div class="format-example">## Seite 1: Seitentitel
Text der Seite (2-4 Sätze).
-----</div>

                <h4>3. Entscheidungen:</h4>
                <div class="format-example">## **ERSTE ENTSCHEIDUNG:**
**Option A:** Beschreibung der ersten Option
**Option B:** Beschreibung der zweiten Option
-----</div>

                <h4>4. Pfade:</h4>
                <div class="format-example"># PFAD A: Pfadname
## Seite 5A: Nächste Seite
Text...
-----</div>

                <h4>5. Enden:</h4>
                <div class="format-example"># ENDE X: Der Weg der Vergebung
## Variante X1: Vollständiger Triumph
Text des Endes...</div>
            </div>
        </div>

        <!-- Upload-Sektion -->
        <div class="upload-section">
            <div class="section-title">📝 Story-Text eingeben</div>
            
            <form id="storyForm">
                <div class="form-group">
                    <label for="rawText">Story-Text (Markdown-Format):</label>
                    <textarea id="rawText" name="rawText" placeholder="Füge hier den Text deiner Geschichte ein...

Beispiel:
# Meine Geschichte
*Eine spannende interaktive Geschichte*
-----

## Seite 1: Der Anfang
Du stehst vor einer Tür. Was machst du?
-----

## **ERSTE ENTSCHEIDUNG:**
**Option A:** Die Tür öffnen
**Option B:** Umkehren
-----

..."></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="genre">Genre:</label>
                        <select id="genre" name="genre">
                            <option value="">Automatisch erkennen</option>
                            <option value="Romance">Romance</option>
                            <option value="Fantasy">Fantasy</option>
                            <option value="Mystery">Mystery</option>
                            <option value="Sci-Fi">Sci-Fi</option>
                            <option value="Thriller">Thriller</option>
                            <option value="Abenteuer">Abenteuer</option>
                            <option value="Drama">Drama</option>
                            <option value="Horror">Horror</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="readingTime">Geschätzte Lesedauer (Minuten):</label>
                        <select id="readingTime" name="readingTime">
                            <option value="">Automatisch berechnen</option>
                            <option value="15">15 Minuten</option>
                            <option value="30">30 Minuten</option>
                            <option value="45">45 Minuten</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="theme">Thema:</label>
                        <input type="text" id="theme" name="theme" placeholder="z.B. Freundschaft, Vertrauen, Liebe">
                    </div>

                    <div class="form-group">
                        <label for="style">Stil:</label>
                        <input type="text" id="style" name="style" placeholder="z.B. spannend, humorvoll, mysteriös">
                    </div>
                </div>

                <div class="form-group">
                    <label for="author">Autor:</label>
                    <input type="text" id="author" name="author" placeholder="Dein Name">
                </div>

                <div class="btn-group">
                    <button type="button" class="btn btn-secondary" onclick="previewStory()">
                        👁️ Vorschau erstellen
                    </button>
                    <button type="button" class="btn btn-primary" onclick="parseStory()">
                        🔄 Parsen & Validieren
                    </button>
                    <button type="button" class="btn btn-success" onclick="saveStory()" id="saveBtn" disabled>
                        💾 Speichern
                    </button>
                </div>
            </form>
        </div>

        <!-- Loading -->
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Verarbeite deine Geschichte...</p>
        </div>

        <!-- Vorschau-Sektion -->
        <div class="preview-section" id="previewSection">
            <div class="section-title">👁️ Story-Vorschau</div>
            <div class="preview-stats" id="previewStats"></div>
            <div id="previewContent"></div>
        </div>

        <!-- Validierungs-Sektion -->
        <div class="preview-section" id="validationSection">
            <div class="section-title">✅ Validierungsergebnis</div>
            <div id="validationContent"></div>
        </div>
    </div>

    <script>
        let currentStory = null;
        let validationReport = null;

        function toggleHelp() {
            const helpContent = document.getElementById('helpContent');
            helpContent.classList.toggle('active');
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function showAlert(message, type = 'error') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            const container = document.querySelector('.container');
            container.insertBefore(alertDiv, container.firstChild);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        async function previewStory() {
            const rawText = document.getElementById('rawText').value.trim();
            
            if (!rawText) {
                showAlert('Bitte gib zuerst den Story-Text ein.');
                return;
            }

            showLoading(true);

            try {
                const response = await fetch('/api/preview-story', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ raw_text: rawText })
                });

                const data = await response.json();

                if (data.success) {
                    displayPreview(data.preview);
                    displayValidation(data.validation_report);
                } else {
                    showAlert(data.error || 'Fehler bei der Vorschau-Erstellung');
                }
            } catch (error) {
                showAlert('Netzwerkfehler: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        async function parseStory() {
            const rawText = document.getElementById('rawText').value.trim();
            
            if (!rawText) {
                showAlert('Bitte gib zuerst den Story-Text ein.');
                return;
            }

            showLoading(true);

            try {
                const metadata = {
                    genre: document.getElementById('genre').value,
                    reading_time: parseInt(document.getElementById('readingTime').value) || undefined,
                    theme: document.getElementById('theme').value,
                    style: document.getElementById('style').value,
                    author: document.getElementById('author').value
                };

                const response = await fetch('/api/parse-story', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ 
                        raw_text: rawText,
                        metadata: metadata
                    })
                });

                const data = await response.json();

                if (data.success) {
                    currentStory = data.story;
                    validationReport = data.validation_report;
                    
                    displayValidation(validationReport);
                    
                    if (validationReport.is_valid) {
                        document.getElementById('saveBtn').disabled = false;
                        showAlert('Story erfolgreich geparst und validiert!', 'success');
                    } else {
                        showAlert('Story geparst, aber es gibt Validierungsfehler.', 'warning');
                    }
                } else {
                    showAlert(data.error || 'Fehler beim Parsen der Story');
                    if (data.validation_report) {
                        displayValidation(data.validation_report);
                    }
                }
            } catch (error) {
                showAlert('Netzwerkfehler: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        async function saveStory() {
            if (!currentStory || !validationReport?.is_valid) {
                showAlert('Bitte parse und validiere die Story zuerst.');
                return;
            }

            showLoading(true);

            try {
                const metadata = {
                    genre: document.getElementById('genre').value,
                    reading_time: parseInt(document.getElementById('readingTime').value) || currentStory.reading_time,
                    theme: document.getElementById('theme').value,
                    style: document.getElementById('style').value,
                    author: document.getElementById('author').value
                };

                const response = await fetch('/api/parse-story', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ 
                        raw_text: document.getElementById('rawText').value,
                        auto_save: true,
                        metadata: metadata
                    })
                });

                const data = await response.json();

                if (data.success && data.story_id) {
                    showAlert('Story erfolgreich gespeichert!', 'success');
                    document.getElementById('saveBtn').disabled = true;
                    
                    // Optional: Weiterleitung zur Story-Übersicht
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 2000);
                } else {
                    showAlert(data.error || 'Fehler beim Speichern der Story');
                }
            } catch (error) {
                showAlert('Netzwerkfehler: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        function displayPreview(preview) {
            const previewSection = document.getElementById('previewSection');
            const previewStats = document.getElementById('previewStats');
            const previewContent = document.getElementById('previewContent');

            // Statistiken anzeigen
            previewStats.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${preview.page_count}</div>
                    <div class="stat-label">Seiten</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${preview.ending_count}</div>
                    <div class="stat-label">Enden</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${preview.choice_count}</div>
                    <div class="stat-label">Entscheidungen</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${preview.estimated_reading_time}</div>
                    <div class="stat-label">Min. Lesezeit</div>
                </div>
            `;

            // Story-Informationen anzeigen
            previewContent.innerHTML = `
                <h3>${preview.title}</h3>
                <p><strong>Beschreibung:</strong> ${preview.description}</p>
                <p><strong>Geschätzte Lesezeit:</strong> ${preview.estimated_reading_time} Minuten</p>
            `;

            previewSection.style.display = 'block';
        }

        function displayValidation(report) {
            const validationSection = document.getElementById('validationSection');
            const validationContent = document.getElementById('validationContent');

            let validationClass = 'validation-success';
            let statusText = 'Validierung erfolgreich';
            let statusIcon = '✅';

            if (!report.is_valid) {
                validationClass = 'validation-error';
                statusText = 'Validierung fehlgeschlagen';
                statusIcon = '❌';
            } else if (report.warnings && report.warnings.length > 0) {
                validationClass = 'validation-warning';
                statusText = 'Validierung mit Warnungen';
                statusIcon = '⚠️';
            }

            let content = `
                <div class="validation-report ${validationClass}">
                    <h4>${statusIcon} ${statusText}</h4>
            `;

            if (report.statistics) {
                content += `
                    <p><strong>Statistiken:</strong></p>
                    <ul class="validation-list">
                        <li>${report.statistics.page_count} Seiten gefunden</li>
                        <li>${report.statistics.ending_count} Enden gefunden</li>
                        <li>${report.statistics.choice_count} Entscheidungen gefunden</li>
                    </ul>
                `;
            }

            if (report.errors && report.errors.length > 0) {
                content += `
                    <p><strong>Fehler:</strong></p>
                    <ul class="validation-list">
                        ${report.errors.map(error => `<li style="color: #ef4444;">${error}</li>`).join('')}
                    </ul>
                `;
            }

            if (report.warnings && report.warnings.length > 0) {
                content += `
                    <p><strong>Warnungen:</strong></p>
                    <ul class="validation-list">
                        ${report.warnings.slice(0, 10).map(warning => `<li style="color: #f59e0b;">${warning}</li>`).join('')}
                        ${report.warnings.length > 10 ? `<li style="color: #94a3b8;">... und ${report.warnings.length - 10} weitere Warnungen</li>` : ''}
                    </ul>
                `;
            }

            content += '</div>';
            validationContent.innerHTML = content;
            validationSection.style.display = 'block';
        }

        // Beispiel-Text laden
        document.addEventListener('DOMContentLoaded', function() {
            // Optional: Beispiel-Text aus der hochgeladenen Datei laden
            // fetch('/upload/Pasted_content.txt')
            //     .then(response => response.text())
            //     .then(text => {
            //         document.getElementById('rawText').value = text;
            //     })
            //     .catch(error => console.log('Beispiel-Text nicht verfügbar'));
        });
    </script>
</body>
</html>

