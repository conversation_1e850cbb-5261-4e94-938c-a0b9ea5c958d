#!/usr/bin/env python3
"""
Test script to add sample stories to the database for testing the story player
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from main import app, db
from story import Story
import json

def add_sample_stories():
    """Add sample stories to the database for testing"""
    
    # Sample story 1 - The Time Travel Mystery
    sample_story_1 = {
        "id": "zeitreise_geheimnis",
        "title": "Das Geheimnis der Zeitreise",
        "description": "Du stehst vor einer mysteriösen Tür in einem verlassenen Herrenhaus. Jede Entscheidung führt dich tiefer in ein Geheimnis, das die Zeit selbst betrifft.",
        "genre": "Mystery",
        "reading_time": 15,
        "theme": "Zeitreise, Geheimnis, Abenteuer",
        "style": "mysteriös, spannend, atmosphärisch",
        "start_page_id": "1",
        "author": "StoryChoice Team",
        "pages": {
            "1": {
                "id": "1",
                "text": "Du stehst vor einer alten Holztür in einem verlassenen Herrenhaus. Ein seltsames, pulsierendes Leuchten dringt durch die Ritzen. Ein kalter Windzug streicht über dein Gesicht, während du überlegst, was sich dahinter verbergen mag.",
                "choices": [
                    {"text": "Die Tür sofort öffnen", "next_page_id": "2A"},
                    {"text": "Erst die Symbole an der Tür untersuchen", "next_page_id": "2B"}
                ]
            },
            "2A": {
                "id": "2A",
                "text": "Mit einem tiefen Atemzug drückst du die Klinke. Die Tür schwingt knarrend auf und gibt den Blick frei auf einen spiralförmigen Korridor, dessen Wände mit leuchtenden Symbolen bedeckt sind. Ein starker Sog zieht dich unwiderstehlich hinein.",
                "choices": [
                    {"text": "Dem Sog folgen", "next_page_id": "3A"},
                    {"text": "Versuchen zu widerstehen", "next_page_id": "3B"}
                ]
            },
            "2B": {
                "id": "2B",
                "text": "Du untersuchst die Symbole genauer. Sie scheinen eine Art Warnung zu sein - alte Runen, die vor Gefahren warnen. Plötzlich verstehst du ihre Bedeutung: 'Nur die Mutigen sollen eintreten, denn die Zeit selbst liegt in euren Händen.'",
                "choices": [
                    {"text": "Trotz der Warnung eintreten", "next_page_id": "2A"},
                    {"text": "Umkehren und gehen", "next_page_id": "ending_safe"}
                ]
            },
            "3A": {
                "id": "3A",
                "text": "Du lässt dich von dem Sog erfassen und wirst durch den Korridor geschleudert. Als du wieder zu dir kommst, findest du dich in einem prächtigen Ballsaal wieder, der von Kerzen erleuchtet wird. Menschen in historischen Kostümen tanzen, als wäre die Zeit stehengeblieben.",
                "choices": [
                    {"text": "Zu den Tanzenden gehen", "next_page_id": "ending_dance"},
                    {"text": "Nach einem Ausgang suchen", "next_page_id": "ending_escape"}
                ]
            },
            "3B": {
                "id": "3B",
                "text": "Du stemmst dich gegen den Sog und schaffst es, dich am Türrahmen festzuhalten. Dabei entdeckst du einen versteckten Mechanismus. Als du ihn betätigst, öffnet sich eine geheime Passage neben der Tür.",
                "choices": [
                    {"text": "Die geheime Passage erkunden", "next_page_id": "ending_secret"},
                    {"text": "Doch durch die Haupttür gehen", "next_page_id": "3A"}
                ]
            },
            "ending_safe": {
                "id": "ending_safe",
                "text": "Du entscheidest dich für die Vorsicht und verlässt das Herrenhaus. Manchmal ist es klüger, Geheimnisse ruhen zu lassen. Du gehst nach Hause, aber die Erinnerung an das mysteriöse Leuchten wird dich für immer begleiten.",
                "is_ending": True,
                "ending_type": "happy_compromise"
            },
            "ending_dance": {
                "id": "ending_dance",
                "text": "Du trittst in den Ballsaal und wirst sofort in den Tanz hineingezogen. Die Zeit verschwimmt, und du tanzt durch die Jahrhunderte. Als der Tanz endet, findest du dich wieder vor dem Herrenhaus - aber du bist um eine magische Erfahrung reicher.",
                "is_ending": True,
                "ending_type": "happy_twist"
            },
            "ending_escape": {
                "id": "ending_escape",
                "text": "Du findest einen Ausgang und entkommst dem zeitlosen Ballsaal. Draußen stellst du fest, dass nur wenige Minuten vergangen sind, obwohl es sich wie Stunden anfühlte. Du hast das Geheimnis der Zeitreise entdeckt und kannst nun zwischen den Zeiten wandeln.",
                "is_ending": True,
                "ending_type": "happy_triumph"
            },
            "ending_secret": {
                "id": "ending_secret",
                "text": "Die geheime Passage führt dich zu einer Bibliothek voller alter Bücher über Zeitreisen. Du verbringst Stunden damit, die Geheimnisse zu studieren, und wirst schließlich zum Hüter dieses Wissens. Ein bittersweet Ende - du hast große Macht erlangt, aber bist nun für immer an diesen Ort gebunden.",
                "is_ending": True,
                "ending_type": "bittersweet"
            }
        }
    }
    
    # Sample story 2 - Romance in Paris
    sample_story_2 = {
        "id": "liebe_in_paris",
        "title": "Liebe in Paris",
        "description": "Eine zufällige Begegnung in einem Pariser Café verändert alles. Wirst du dein Herz öffnen oder auf Nummer sicher gehen?",
        "genre": "Romance",
        "reading_time": 20,
        "theme": "Liebe, Paris, Entscheidungen",
        "style": "romantisch, emotional, herzerwärmend",
        "start_page_id": "1",
        "author": "StoryChoice Team",
        "pages": {
            "1": {
                "id": "1",
                "text": "Du sitzt in einem gemütlichen Café in Montmartre und beobachtest das Treiben auf der Straße. Plötzlich stößt jemand gegen deinen Tisch und verschüttet Kaffee über deine Notizen. Ein attraktiver Fremder entschuldigt sich profusely.",
                "choices": [
                    {"text": "Freundlich lächeln und sagen, dass es kein Problem ist", "next_page_id": "2A"},
                    {"text": "Verärgert reagieren wegen der ruinierten Notizen", "next_page_id": "2B"}
                ]
            },
            "2A": {
                "id": "2A",
                "text": "Dein freundliches Lächeln entspannt die Situation sofort. Der Fremde stellt sich als Alex vor und bietet an, dir einen neuen Kaffee zu kaufen. Ihr kommt ins Gespräch und entdeckt gemeinsame Interessen. Die Zeit vergeht wie im Flug.",
                "choices": [
                    {"text": "Alex zu einem Spaziergang durch Paris einladen", "next_page_id": "3A"},
                    {"text": "Höflich ablehnen und allein bleiben", "next_page_id": "ending_missed"}
                ]
            },
            "2B": {
                "id": "2B",
                "text": "Deine verärgerte Reaktion macht Alex sichtlich nervös. Er entschuldigt sich mehrmals und bietet an, für die Reinigung zu zahlen. Du merkst, dass er wirklich aufrichtig ist und es ihm leid tut. Vielleicht warst du zu hart.",
                "choices": [
                    {"text": "Dich entschuldigen und freundlicher werden", "next_page_id": "2A"},
                    {"text": "Bei deiner ablehnenden Haltung bleiben", "next_page_id": "ending_alone"}
                ]
            },
            "3A": {
                "id": "3A",
                "text": "Ihr spaziert zusammen durch die romantischen Straßen von Paris. Alex zeigt dir versteckte Orte, die nur Einheimische kennen. Bei Sonnenuntergang erreicht ihr den Sacré-Cœur und die Stadt liegt euch zu Füßen. Die Atmosphäre ist magisch.",
                "choices": [
                    {"text": "Alex deine Gefühle gestehen", "next_page_id": "ending_love"},
                    {"text": "Den Moment einfach genießen ohne Worte", "next_page_id": "ending_friendship"}
                ]
            },
            "ending_missed": {
                "id": "ending_missed",
                "text": "Du entscheidest dich, allein zu bleiben und verpasst eine wunderbare Gelegenheit. Später bereust du deine Zurückhaltung. Manchmal muss man Risiken eingehen, um das Glück zu finden. Du lernst, beim nächsten Mal mutiger zu sein.",
                "is_ending": True,
                "ending_type": "bittersweet"
            },
            "ending_alone": {
                "id": "ending_alone",
                "text": "Du bleibst bei deiner ablehnenden Haltung und Alex geht enttäuscht. Du verbringst den Rest des Tages allein in Paris und fragst dich, was hätte sein können. Manchmal ist Stolz ein teurer Luxus.",
                "is_ending": True,
                "ending_type": "tragic"
            },
            "ending_love": {
                "id": "ending_love",
                "text": "Du gestehst Alex deine Gefühle und zu deiner Freude erwidert er sie. Ihr verbringt eine magische Nacht in Paris und plant bereits euer nächstes Treffen. Die Liebe hat euch in der Stadt der Liebe gefunden - ein perfektes Happy End.",
                "is_ending": True,
                "ending_type": "happy_triumph"
            },
            "ending_friendship": {
                "id": "ending_friendship",
                "text": "Ihr genießt den Moment in stiller Verbundenheit. Auch ohne Worte spürt ihr eine besondere Verbindung. Ihr tauscht Kontaktdaten aus und freut euch auf eine tiefe Freundschaft, die vielleicht zu mehr werden könnte. Ein hoffnungsvoller Neuanfang.",
                "is_ending": True,
                "ending_type": "happy_compromise"
            }
        }
    }
    
    with app.app_context():
        # Check if stories already exist
        existing_story_1 = Story.query.filter_by(id=sample_story_1["id"]).first()
        existing_story_2 = Story.query.filter_by(id=sample_story_2["id"]).first()
        
        if not existing_story_1:
            story_1 = Story(
                id=sample_story_1["id"],
                title=sample_story_1["title"],
                description=sample_story_1["description"],
                genre=sample_story_1["genre"],
                reading_time=sample_story_1["reading_time"],
                theme=sample_story_1["theme"],
                style=sample_story_1["style"],
                start_page_id=sample_story_1["start_page_id"],
                pages=sample_story_1["pages"],
                author=sample_story_1["author"]
            )
            story_1.is_published = True
            db.session.add(story_1)
            print(f"Added story: {sample_story_1['title']}")
        else:
            print(f"Story already exists: {sample_story_1['title']}")
            
        if not existing_story_2:
            story_2 = Story(
                id=sample_story_2["id"],
                title=sample_story_2["title"],
                description=sample_story_2["description"],
                genre=sample_story_2["genre"],
                reading_time=sample_story_2["reading_time"],
                theme=sample_story_2["theme"],
                style=sample_story_2["style"],
                start_page_id=sample_story_2["start_page_id"],
                pages=sample_story_2["pages"],
                author=sample_story_2["author"]
            )
            story_2.is_published = True
            db.session.add(story_2)
            print(f"Added story: {sample_story_2['title']}")
        else:
            print(f"Story already exists: {sample_story_2['title']}")
        
        db.session.commit()
        print("Sample stories added successfully!")

if __name__ == "__main__":
    add_sample_stories()
