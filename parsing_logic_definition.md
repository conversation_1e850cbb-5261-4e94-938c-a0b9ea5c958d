# Analyse des Eingabeformats und Definition der Parsing-Logik

## 1. Überblick über das Eingabeformat

Das bereitgestellte Textformat für Geschichten ist eine strukturierte Markdown-ähnliche Darstellung, die speziell für interaktive Erzählungen mit Verzweigungen konzipiert wurde. Es enthält klare Indikatoren für Seiten, Entscheidungspunkte und Pfade, sowie Metadaten für die Geschichte selbst. Das Ziel ist es, diesen Rohtext in das bereits definierte JSON-Schema für interaktive Geschichten zu überführen, das vom Backend-API verarbeitet werden kann.

### 1.1 Allgemeine Strukturmerkmale

- **Titel und Beschreibung der Geschichte**: Am Anfang des Dokuments, gefolgt von einer Trennlinie (`-----`).
- **Seiten**: Jede Seite beginnt mit einer Überschrift der Form `## Seite X: Titel` (z.B. `## Seite 1: <PERSON>`).
- **Seiteninhalt**: Der Textinhalt einer Seite folgt direkt auf die Seitenüberschrift und endet vor der nächsten Überschrift oder Trennlinie.
- **Trennlinien**: `-----` wird verwendet, um Abschnitte (Metadaten, Seiten, Entscheidungen, Pfade, Enden) voneinander abzugrenzen.
- **Pfade**: Abschnitte wie `# PFAD A: Der Vertrauensweg` oder `# PFAD B: Der Justizweg` kennzeichnen den Beginn eines neuen Handlungsstrangs nach einer Entscheidung.
- **Enden**: Abschnitte wie `# ENDE X: Der Weg der Vergebung` kennzeichnen die finalen Enden, die wiederum Varianten wie `## Variante X1: Vollständiger Triumph` enthalten können.

### 1.2 Spezifische Marker und ihre Bedeutung

#### 1.2.1 Seiten-Identifikation

Seiten werden durch `## Seite [ID]: [Titel]` identifiziert. Die `[ID]` ist hierbei entscheidend für die Verknüpfung der Seiten im Entscheidungsbaum. Beispiele für IDs sind `1`, `2A`, `2B`, `9A1`, `9B2`.

#### 1.2.2 Entscheidungspunkte

Entscheidungspunkte sind durch fettgedruckte Überschriften wie `## **ERSTE ENTSCHEIDUNG:**`, `## **ZWEITE ENTSCHEIDUNG (PFAD A):**` oder `## **FINALE ENTSCHEIDUNG**` gekennzeichnet. Diese Abschnitte enthalten die Optionen, die der Spieler wählen kann.

#### 1.2.3 Optionen innerhalb von Entscheidungen

Optionen werden mit `**Option [Buchstabe/Nummer]:** [Beschreibung]` formatiert. Der Text nach dem Doppelpunkt ist der Anzeigetext für die Entscheidung. Die Zuordnung zur nächsten Seite muss aus dem Kontext des Pfades und der nachfolgenden Seiten-IDs abgeleitet werden.

Beispiel:
```
**Option A:** Du beschließt, zuerst direkt mit Lukas zu sprechen und seine Seite der Geschichte zu hören, bevor ihr drastische Schritte unternehmt.

**Option B:** Du stimmst Marina zu und ihr wendet euch gemeinsam an die Polizei, um den Diebstahl zu melden.
```

Nach `**Option A:**` folgt der Inhalt von `PFAD A`, beginnend mit `Seite 5A`. Dies impliziert, dass `Option A` zu `Seite 5A` führt. Ähnlich verhält es sich mit `Option B` und `Seite 5B`.

#### 1.2.4 Ende-Varianten

Enden sind durch `## Variante [X/Y/Z][Nummer]: [Titel]` gekennzeichnet. Der Text darunter ist der Inhalt des Endes. Diese Seiten sind `is_ending: true` und haben einen `ending_type` basierend auf der Variante (z.B. `X1` -> `happy_triumph`).

## 2. Definition der Parsing-Logik

Die Parsing-Logik wird in mehreren Schritten erfolgen, um den Rohtext in das gewünschte JSON-Format zu transformieren.

### 2.1 Schritt 1: Aufteilen des Rohtextes

Der erste Schritt besteht darin, den gesamten Rohtext in logische Blöcke zu zerlegen. Dies kann durch das Erkennen der Trennlinien (`-----`) und der Überschriften (`#` oder `##`) erfolgen.

- **Metadaten-Block**: Alles vor der ersten Seitenüberschrift.
- **Seiten-Blöcke**: Jeder Block, der mit `## Seite` beginnt und vor der nächsten `## Seite`, `## **ENTCHEIDUNG**` oder `-----` endet.
- **Entscheidungs-Blöcke**: Jeder Block, der mit `## **ENTCHEIDUNG**` beginnt und vor der nächsten `## Seite` oder `-----` endet.
- **Pfad-Blöcke**: Jeder Block, der mit `# PFAD` beginnt und vor dem nächsten `# PFAD` oder `# ENDE` endet.
- **End-Blöcke**: Jeder Block, der mit `# ENDE` beginnt und vor dem nächsten `# ENDE` oder dem Ende des Dokuments endet.

### 2.2 Schritt 2: Extrahieren der Story-Metadaten

Aus dem Metadaten-Block am Anfang des Dokuments werden der Titel und die Beschreibung extrahiert. Andere Metadaten wie Genre, Lesedauer, Thema und Stil müssen entweder manuell hinzugefügt oder aus dem Kontext abgeleitet werden, falls sie nicht explizit im Rohtext vorhanden sind. Für den Anfang können diese als Platzhalter gesetzt werden.

### 2.3 Schritt 3: Parsen der Seiten

Für jeden Seiten-Block wird Folgendes extrahiert:

- **`id`**: Die Seiten-ID aus der Überschrift (z.B. `1`, `2A`, `9A1`).
- **`text`**: Der gesamte Textinhalt der Seite. Es muss überprüft werden, ob die Textlänge (Anzahl der Sätze) den Anforderungen (2-4 Sätze) entspricht.
- **`is_ending`**: Standardmäßig `false`. Wird auf `true` gesetzt, wenn die Seite in einem End-Block (`# ENDE`) gefunden wird.
- **`ending_type`**: Wird nur für End-Seiten gesetzt, basierend auf der `Variante` (z.B. `X1` -> `happy_triumph`).
- **`choices`**: Eine leere Liste, die später gefüllt wird.

### 2.4 Schritt 4: Parsen der Entscheidungen und Verknüpfen der Pfade

Dies ist der komplexeste Teil. Für jeden Entscheidungs-Block:

- **Extrahieren der Optionen**: Jede `**Option [Buchstabe/Nummer]:** [Beschreibung]` wird als `text` für eine Entscheidung extrahiert.
- **Bestimmen der `next_page_id`**: Dies erfordert eine vorausschauende Logik. Wenn `Option A` in einem `ERSTE ENTSCHEIDUNG` Block gefunden wird, muss der Parser wissen, dass die nächste Seite in `PFAD A` die Zielseite ist. Dies kann durch das Scannen der nachfolgenden `PFAD`-Blöcke und deren erster Seiten-ID erfolgen.

**Beispiel-Logik für `next_page_id`:**
- Wenn `ERSTE ENTSCHEIDUNG` mit `Option A` und `Option B`:
    - `Option A` führt zur ersten Seite in `PFAD A` (z.B. `5A`).
    - `Option B` führt zur ersten Seite in `PFAD B` (z.B. `5B`).
- Wenn `ZWEITE ENTSCHEIDUNG (PFAD A)` mit `Option A1` und `Option A2`:
    - `Option A1` führt zur ersten Seite in `PFAD A1` (z.B. `9A1`).
    - `Option A2` führt zur ersten Seite in `PFAD A2` (z.B. `9A2`).
- Wenn `FINALE ENTSCHEIDUNG` mit `Option X`, `Option Y`, `Option Z`:
    - `Option X` führt zur ersten Seite in `ENDE X` (z.B. `Variante X1`).
    - `Option Y` führt zur ersten Seite in `ENDE Y` (z.B. `Variante Y1`).
    - `Option Z` führt zur ersten Seite in `ENDE Z` (z.B. `Variante Z1`).

Diese Zuordnung erfordert, dass die Seiten-IDs innerhalb der Pfade und Enden konsistent benannt sind und dass die Pfad- und End-Blöcke direkt auf die entsprechenden Entscheidungs-Blöcke folgen.

### 2.5 Schritt 5: Validierung und Fehlerbehandlung

Nach dem Parsen muss das generierte JSON-Objekt gegen das Story-Schema validiert werden. Dies umfasst:

- **Vollständigkeit**: Sind alle erforderlichen Felder vorhanden?
- **Referenzintegrität**: Existieren alle `next_page_id`s tatsächlich als Seiten-IDs?
- **Textlänge**: Entspricht der Text jeder Seite der 2-4 Sätze Regel?
- **Entscheidungsbaum-Logik**: Sind die Verzweigungen korrekt? Führen alle Pfade zur finalen Konvergenz? Sind die Enden korrekt klassifiziert?

Fehler sollten detailliert gemeldet werden, um dem Benutzer die Korrektur des Rohtextes zu erleichtern.

## 3. Datenmodellierung für das Parsing

Das Backend wird eine neue API-Route erhalten, die den Rohtext entgegennimmt und das geparste JSON zurückgibt. Die interne Verarbeitung wird Klassen oder Funktionen verwenden, die die oben beschriebene Logik implementieren.

```python
class StoryParser:
    def __init__(self, raw_text):
        self.raw_text = raw_text
        self.parsed_story = {
            "id": "",
            "title": "",
            "description": "",
            "genre": "", # Muss manuell/Standard gesetzt werden
            "reading_time": 0, # Muss manuell/Standard gesetzt werden
            "theme": "", # Muss manuell/Standard gesetzt werden
            "style": "", # Muss manuell/Standard gesetzt werden
            "start_page_id": "1",
            "author": "",
            "pages": {}
        }
        self.sections = self._split_sections()

    def _split_sections(self):
        # Logik zum Aufteilen des Textes in Abschnitte
        pass

    def _parse_metadata(self):
        # Logik zum Extrahieren von Titel und Beschreibung
        pass

    def _parse_pages(self):
        # Logik zum Extrahieren von Seiten-IDs, Texten, End-Typen
        pass

    def _parse_decisions_and_link_paths(self):
        # Logik zum Extrahieren von Optionen und Verknüpfen mit next_page_id
        pass

    def parse(self):
        self._parse_metadata()
        self._parse_pages()
        self._parse_decisions_and_link_paths()
        # Weitere Parsing-Schritte
        return self.parsed_story

    def validate(self):
        # Validierungslogik gegen das Story-Schema
        pass
```

## 4. Beispiel für die Zuordnung von Optionen zu Seiten-IDs

Um die `next_page_id` für jede Option zu bestimmen, kann ein Mapping oder eine heuristische Suche verwendet werden. Die Annahme ist, dass die Pfad-Überschriften (`# PFAD A: ...`) direkt auf die Entscheidungs-Blöcke folgen und die erste Seite innerhalb dieses Pfades die Zielseite ist.

```
## **ERSTE ENTSCHEIDUNG:**

**Option A:** ...

**Option B:** ...

-----

# PFAD A: Der Vertrauensweg

## Seite 5A: Die Suche
...

# PFAD B: Der Justizweg

## Seite 5B: Der Gang zur Polizei
...
```

In diesem Fall würde der Parser erkennen, dass `Option A` zu `5A` und `Option B` zu `5B` führt. Dies erfordert eine sorgfältige Implementierung, die die Reihenfolge der Blöcke im Rohtext berücksichtigt.

## 5. Herausforderungen und Überlegungen

- **Robustheit des Parsers**: Der Parser muss robust genug sein, um leichte Abweichungen im Eingabeformat zu tolerieren, aber streng genug, um ungültige Strukturen zu erkennen.
- **Mehrdeutigkeiten**: Bestimmte Formulierungen oder fehlende Marker könnten zu Mehrdeutigkeiten führen, die manuell behoben werden müssen oder zu Fehlern führen.
- **Performance**: Bei sehr langen Geschichten könnte das Parsen und Validieren rechenintensiv werden.
- **Manuelle Metadaten**: Genre, Lesedauer, Thema und Stil sind nicht direkt im Rohtext enthalten und müssen entweder manuell eingegeben oder durch NLP-Techniken abgeleitet werden.

Diese Analyse bildet die Grundlage für die Entwicklung der Backend-Parsing-API und die Erweiterung des Frontend-Upload-Interfaces.

