# Integration des Story-Upload-Systems in die Hauptwebseite

## 1. Überblick

Dieses Dokument beschreibt die Integration des zuvor entwickelten Story-Upload-Systems in die Hauptwebseite der interaktiven Story-PWA. Es behandelt die Anpassungen an der Frontend-Anwendung, die Interaktion mit dem Backend-API und die Bereitstellung der gesamten Anwendung.

## 2. Frontend-Anpassungen (index.html)

Die Hauptwebseite (`index.html`) wurde erweitert, um dynamisch Geschichten vom Backend zu laden und einen interaktiven Story-Player zu integrieren. Die wichtigsten Änderungen umfassen:

### 2.1 API-Integration für Story-Listing

Die Sektion "Beliebte Geschichten" (`#stories`) lädt nun Geschichten über die `/api/stories`-Endpunkt des Flask-Backends. Bei einem Fehler beim Laden der Geschichten wird auf statische Beispieldaten zurückgegriffen.

```javascript
async function loadStoriesFromAPI() {
    try {
        showLoading(true);
        const response = await fetch("/api/stories"); // API-Aufruf zum Backend
        const data = await response.json();
        
        if (data.success) {
            currentStories = data.stories;
            filteredStories = [...currentStories];
            renderStories();
            updateResultsCount();
        } else {
            throw new Error(data.error || "Fehler beim Laden der Geschichten");
        }
    } catch (error) {
        console.error("Fehler beim Laden der Stories:", error);
        showError("Fehler beim Laden der Geschichten. Lade Beispiel-Geschichten...");
        loadFallbackStories(); // Fallback zu statischen Daten
    } finally {
        showLoading(false);
    }
}

function loadFallbackStories() {
    // Statische Beispieldaten, falls Backend nicht erreichbar ist
    currentStories = [
        // ... (Beispielgeschichten)
    ];
    filteredStories = [...currentStories];
    renderStories();
    updateResultsCount();
}
```

### 2.2 Interaktiver Story-Player

Ein neuer Story-Player (`StoryPlayer` Klasse) wurde implementiert, der es ermöglicht, Geschichten direkt auf der Webseite abzuspielen. Dieser Player interagiert mit dem Backend, um die vollständigen Story-Details (inklusive Seiten und Entscheidungen) über den `/api/stories/{story_id}`-Endpunkt abzurufen.

```javascript
async function openStory(storyId) {
    try {
        const response = await fetch(`/api/stories/${storyId}`); // Abruf der vollständigen Story-Daten
        const data = await response.json();
        
        if (data.success) {
            currentStoryPlayer = new StoryPlayer(data.story);
            currentStoryPlayer.start();
            document.getElementById("storyModal").classList.add("active");
        } else {
            throw new Error(data.error || "Story nicht gefunden");
        }
    } catch (error) {
        console.error("Fehler beim Laden der Story:", error);
        alert("Fehler beim Laden der Geschichte. Versuche es später erneut.");
    }
}

class StoryPlayer {
    constructor(storyData) {
        this.story = storyData;
        this.currentPageId = storyData.start_page_id;
        this.container = document.getElementById("storyPlayerContent");
    }
    
    start() {
        this.renderCurrentPage();
    }
    
    renderCurrentPage() {
        const page = this.story.pages[this.currentPageId];
        // ... (Rendering-Logik für Seiten und Entscheidungen)
    }
    
    makeChoice(nextPageId) {
        this.currentPageId = nextPageId;
        this.renderCurrentPage();
    }
    
    restart() {
        this.currentPageId = this.story.start_page_id;
        this.renderCurrentPage();
    }
}
```

### 2.3 PWA-Manifest und Service Worker (Vorbereitung)

Ein `manifest.json` wurde erstellt, um die PWA-Funktionalität zu ermöglichen (Installation auf dem Startbildschirm, Offline-Fähigkeit). Die Implementierung eines Service Workers für intelligentes Caching ist für zukünftige Schritte vorgesehen, um die Offline-Funktionalität vollständig zu gewährleisten.

```json
{
  "name": "StoryChoice - Interaktive Geschichten",
  "short_name": "StoryChoice",
  "description": "Erlebe interaktive Geschichten mit Entscheidungen, die den Verlauf bestimmen",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#0f0f23",
  "theme_color": "#6366f1",
  "orientation": "portrait-primary",
  "icons": [
    // ... (Icons)
  ],
  "categories": ["entertainment", "books", "games"],
  "lang": "de",
  "dir": "ltr"
}
```

## 3. Backend-Integration (Flask-App)

Das Flask-Backend (`story_backend`) dient als API-Server für die Story-Daten. Es ist so konfiguriert, dass es Cross-Origin Requests (CORS) vom Frontend akzeptiert und sowohl die Story-Listing- als auch die Story-Detail-Endpunkte bereitstellt.

### 3.1 CORS-Konfiguration

Die Flask-Anwendung ist mit `flask_cors` konfiguriert, um Anfragen von verschiedenen Ursprüngen zu ermöglichen, was für die Frontend-Backend-Kommunikation unerlässlich ist.

```python
from flask_cors import CORS
# ...
app = Flask(__name__, static_folder=os.path.join(os.path.dirname(__file__), 'static'))
CORS(app) # CORS für alle Routen aktivieren
```

### 3.2 Statische Dateien und Routing

Das Flask-Backend dient auch dazu, die statischen Frontend-Dateien (HTML, CSS, JS, Manifest) zu servieren. Dies vereinfacht das Deployment und vermeidet CORS-Probleme, wenn Frontend und Backend vom selben Ursprung kommen.

```python
@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def serve(path):
    static_folder_path = app.static_folder
    if static_folder_path is None:
            return "Static folder not configured", 404

    if path != "" and os.path.exists(os.path.join(static_folder_path, path)):
        return send_from_directory(static_folder_path, path)
    else:
        index_path = os.path.join(static_folder_path, 'index.html')
        if os.path.exists(index_path):
            return send_from_directory(static_folder_path, 'index.html')
        else:
            return "index.html not found", 404
```

## 4. Deployment und Betrieb

Das System ist für das Deployment als Ganzes konzipiert, wobei das Flask-Backend sowohl die API als auch die statischen Frontend-Dateien bereitstellt.

### 4.1 Lokaler Betrieb

Für den lokalen Test kann das Flask-Backend gestartet werden:

```bash
cd story_backend
source venv/bin/activate
python src/main.py
```

Die Hauptwebseite ist dann unter `http://localhost:5001/` erreichbar, und das Story-Upload-Interface unter `http://localhost:5001/story-upload.html`.

### 4.2 Produktions-Deployment (Empfehlung)

Für ein Produktions-Deployment wird empfohlen, einen WSGI-Server wie Gunicorn zu verwenden und die Anwendung auf einer geeigneten Plattform bereitzustellen (z.B. einem VPS oder einem Cloud-Dienst).

```bash
# Im story_backend Verzeichnis
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5001 src.main:app
```

Es ist wichtig, Umgebungsvariablen für sensible Daten (z.B. `SECRET_KEY`) zu setzen und die Datenbankverbindung entsprechend anzupassen.

## 5. Fazit

Die Integration des Story-Upload-Systems in die Hauptwebseite ist abgeschlossen. Die Anwendung kann nun dynamisch Geschichten laden und abspielen, und bietet eine Grundlage für eine voll funktionsfähige interaktive Story-PWA. Zukünftige Schritte könnten die Implementierung eines Service Workers für erweiterte Offline-Fähigkeiten und die Verfeinerung des Designs umfassen.

