from flask_sqlalchemy import SQLAlchemy
import json
from datetime import datetime

db = SQLAlchemy()

class Story(db.Model):
    __tablename__ = 'stories'
    
    id = db.Column(db.String(100), primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=False)
    genre = db.Column(db.String(50), nullable=False)
    reading_time = db.Column(db.Integer, nullable=False)  # in minutes
    theme = db.Column(db.String(200), nullable=False)
    style = db.Column(db.String(200), nullable=False)
    start_page_id = db.Column(db.String(50), nullable=False)
    pages_json = db.Column(db.Text, nullable=False)  # JSON string of pages
    author = db.Column(db.String(100), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_published = db.Column(db.Boolean, default=False)
    
    def __init__(self, id, title, description, genre, reading_time, theme, style, start_page_id, pages, author=None):
        self.id = id
        self.title = title
        self.description = description
        self.genre = genre
        self.reading_time = reading_time
        self.theme = theme
        self.style = style
        self.start_page_id = start_page_id
        self.pages_json = json.dumps(pages)
        self.author = author
    
    @property
    def pages(self):
        """Get pages as Python dict"""
        return json.loads(self.pages_json)
    
    @pages.setter
    def pages(self, value):
        """Set pages from Python dict"""
        self.pages_json = json.dumps(value)
    
    def to_dict(self):
        """Convert story to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'genre': self.genre,
            'reading_time': self.reading_time,
            'theme': self.theme,
            'style': self.style,
            'start_page_id': self.start_page_id,
            'pages': self.pages,
            'author': self.author,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'is_published': self.is_published
        }
    
    def validate_story_structure(self):
        """Validate that the story follows the required schema"""
        errors = []
        
        # Check if start_page_id exists in pages
        if self.start_page_id not in self.pages:
            errors.append(f"Start page '{self.start_page_id}' not found in pages")
        
        # Validate each page
        for page_id, page in self.pages.items():
            page_errors = self._validate_page(page_id, page)
            errors.extend(page_errors)
        
        return errors
    
    def _validate_page(self, page_id, page):
        """Validate a single page structure"""
        errors = []
        
        # Required fields
        required_fields = ['id', 'text']
        for field in required_fields:
            if field not in page:
                errors.append(f"Page '{page_id}': Missing required field '{field}'")
        
        # Check if page ID matches
        if page.get('id') != page_id:
            errors.append(f"Page '{page_id}': ID mismatch in page data")
        
        # Validate text length (2-4 sentences)
        text = page.get('text', '')
        sentence_count = len([s for s in text.split('.') if s.strip()])
        if sentence_count < 2 or sentence_count > 4:
            errors.append(f"Page '{page_id}': Text should be 2-4 sentences, found {sentence_count}")
        
        # Validate choices if present
        if 'choices' in page:
            for i, choice in enumerate(page['choices']):
                if 'text' not in choice:
                    errors.append(f"Page '{page_id}', Choice {i}: Missing 'text' field")
                if 'next_page_id' not in choice:
                    errors.append(f"Page '{page_id}', Choice {i}: Missing 'next_page_id' field")
                elif choice['next_page_id'] not in self.pages:
                    errors.append(f"Page '{page_id}', Choice {i}: next_page_id '{choice['next_page_id']}' not found")
        
        # Validate ending pages
        if page.get('is_ending', False):
            if 'ending_type' not in page:
                errors.append(f"Page '{page_id}': Ending page missing 'ending_type'")
            elif page['ending_type'] not in ['happy_triumph', 'happy_compromise', 'happy_twist', 'tragic', 'open', 'bittersweet']:
                errors.append(f"Page '{page_id}': Invalid ending_type '{page['ending_type']}'")
        
        return errors
    
    def get_page_count(self):
        """Get total number of pages in the story"""
        return len(self.pages)
    
    def get_ending_count(self):
        """Get number of ending pages"""
        return len([page for page in self.pages.values() if page.get('is_ending', False)])
    
    def get_choice_count(self):
        """Get total number of choices across all pages"""
        total_choices = 0
        for page in self.pages.values():
            if 'choices' in page:
                total_choices += len(page['choices'])
        return total_choices

class StoryTemplate(db.Model):
    __tablename__ = 'story_templates'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=False)
    template_json = db.Column(db.Text, nullable=False)  # JSON template structure
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __init__(self, name, description, template):
        self.name = name
        self.description = description
        self.template_json = json.dumps(template)
    
    @property
    def template(self):
        """Get template as Python dict"""
        return json.loads(self.template_json)
    
    @template.setter
    def template(self, value):
        """Set template from Python dict"""
        self.template_json = json.dumps(value)
    
    def to_dict(self):
        """Convert template to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'template': self.template,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

